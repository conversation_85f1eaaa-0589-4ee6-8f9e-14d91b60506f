package com.ruoyi.app.controller;

import java.math.BigDecimal;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fuguang.domain.AppTask;
import com.ruoyi.fuguang.service.IAppTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.domain.TaskPayment;
import com.ruoyi.fuguang.domain.WithdrawRecord;
import com.ruoyi.fuguang.service.ITaskPaymentService;
import com.ruoyi.fuguang.service.IWithdrawService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * APP任务支付接口
 * 
 * <AUTHOR>
 */
@Api(tags = "APP任务支付接口")
@RestController("appTaskPaymentController")
@RequestMapping("/app/task/payment")
public class AppTaskPaymentController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(AppTaskPaymentController.class);

    @Autowired
    private ITaskPaymentService taskPaymentService;

    @Autowired
    private IAppTaskService appTaskService;

    /**
     * 根据任务订单编号获取支付信息
     */
    @ApiOperation("根据任务订单编号获取支付信息")
    @PostMapping("/{taskId}")
    public AjaxResult testPaymentSuccess(@PathVariable("taskId") Long taskId)
    {
        AppTask appTask=appTaskService.selectAppTaskByTaskId(taskId);
        if(StringUtils.isNull(appTask)){
            return error("任务不存在");
        }
        // 任务发布成功，创建支付订单
        TaskPayment payData= taskPaymentService.createTaskPayOrder(appTask, "1");
        // 返回任务信息和支付参数
        Map<String, Object> responseData = new java.util.HashMap<>();
        responseData.put("taskId", appTask.getTaskId());
        responseData.put("taskTitle", appTask.getTaskTitle());
        responseData.put("taskAmount", appTask.getTaskAmount());
        responseData.put("maintenanceFee", appTask.getTaskAmount().multiply(appTask.getMaintenanceFeeProportion()));
        responseData.put("guarantee", appTask.getTaskAmount().multiply(appTask.getGuaranteeProportion()));
        responseData.put("paymentInfo", payData);
        return AjaxResult.success(responseData);
    }

    /**
     * 查询我的任务支付记录
     */
    @ApiOperation("查询我的任务支付记录")
    @GetMapping("/my-payments")
    public AjaxResult getMyPayments()
    {
        try {
            Long userId = getUserId();
            List<TaskPayment> payments = taskPaymentService.selectTaskPaymentListByUserId(userId);
            return success(payments);
        } catch (Exception e) {
            log.error("查询支付记录异常", e);
            return error("查询支付记录失败：" + e.getMessage());
        }
    }




}
