-- 支付系统测试数据
-- 执行前请确保已经创建了相关表结构

-- 清空现有测试数据（可选）
-- DELETE FROM payment_callback_log;
-- DELETE FROM task_payment;
-- DELETE FROM mall_payment;
-- DELETE FROM offline_payment;

-- 插入任务支付测试数据
INSERT INTO `task_payment` (`payment_id`, `task_id`, `task_title`, `user_id`, `user_name`, `order_no`, `pay_amount`, `pay_type`, `pay_status`, `trade_no`, `pay_time`, `notify_time`, `create_time`, `update_time`) VALUES
(1001, 1, '数据录入任务', 1001, '张三', 'TASK202501270001', 50.00, '1', '1', '2025012722001234567890123456', '2025-01-27 09:15:30', '2025-01-27 09:15:35', '2025-01-27 09:10:00', '2025-01-27 09:15:35'),
(1002, 2, '问卷调研任务', 1002, '李四', 'TASK202501270002', 30.00, '2', '1', '4200001234567890123456789012', '2025-01-27 10:22:15', '2025-01-27 10:22:20', '2025-01-27 10:18:00', '2025-01-27 10:22:20'),
(1003, 3, '图片标注任务', 1003, '王五', 'TASK202501270003', 80.00, '1', '1', '2025012722001234567890123457', '2025-01-27 11:30:45', '2025-01-27 11:30:50', '2025-01-27 11:25:00', '2025-01-27 11:30:50'),
(1004, 4, '文档整理任务', 1004, '赵六', 'TASK202501270004', 25.00, '3', '1', '', '2025-01-27 14:15:20', '2025-01-27 14:15:20', '2025-01-27 14:10:00', '2025-01-27 14:15:20'),
(1005, 5, '数据分析任务', 1005, '钱七', 'TASK202501270005', 120.00, '1', '0', '', NULL, NULL, '2025-01-27 15:30:00', '2025-01-27 15:30:00'),
(1006, 6, '内容审核任务', 1006, '孙八', 'TASK202501270006', 40.00, '2', '2', '', NULL, NULL, '2025-01-27 16:45:00', '2025-01-27 16:45:00'),
(1007, 7, '翻译任务', 1007, '周九', 'TASK202501270007', 200.00, '1', '1', '2025012722001234567890123458', '2025-01-27 17:20:30', '2025-01-27 17:20:35', '2025-01-27 17:15:00', '2025-01-27 17:20:35'),
(1008, 8, '设计任务', 1008, '吴十', 'TASK202501270008', 150.00, '1', '0', '', NULL, NULL, '2025-01-27 18:00:00', '2025-01-27 18:00:00');

-- 插入商城支付测试数据
INSERT INTO `mall_payment` (`payment_id`, `order_id`, `order_no`, `user_id`, `pay_amount`, `pay_type`, `pay_status`, `trade_no`, `pay_time`, `notify_time`, `create_time`, `update_time`) VALUES
(2001, 1001, 'MALL202501270001', 1001, 299.00, '1', '1', '2025012722001234567890123459', '2025-01-27 08:30:15', '2025-01-27 08:30:20', '2025-01-27 08:25:00', '2025-01-27 08:30:20'),
(2002, 1002, 'MALL202501270002', 1002, 199.00, '2', '1', '4200001234567890123456789013', '2025-01-27 09:45:30', '2025-01-27 09:45:35', '2025-01-27 09:40:00', '2025-01-27 09:45:35'),
(2003, 1003, 'MALL202501270003', 1003, 599.00, '1', '1', '2025012722001234567890123460', '2025-01-27 12:15:45', '2025-01-27 12:15:50', '2025-01-27 12:10:00', '2025-01-27 12:15:50'),
(2004, 1004, 'MALL202501270004', 1004, 99.00, '3', '1', '', '2025-01-27 13:20:10', '2025-01-27 13:20:10', '2025-01-27 13:15:00', '2025-01-27 13:20:10'),
(2005, 1005, 'MALL202501270005', 1005, 399.00, '1', '0', '', NULL, NULL, '2025-01-27 14:30:00', '2025-01-27 14:30:00'),
(2006, 1006, 'MALL202501270006', 1006, 799.00, '2', '2', '', NULL, NULL, '2025-01-27 15:45:00', '2025-01-27 15:45:00'),
(2007, 1007, 'MALL202501270007', 1007, 159.00, '1', '1', '2025012722001234567890123461', '2025-01-27 16:10:25', '2025-01-27 16:10:30', '2025-01-27 16:05:00', '2025-01-27 16:10:30'),
(2008, 1008, 'MALL202501270008', 1008, 259.00, '2', '1', '4200001234567890123456789014', '2025-01-27 19:30:40', '2025-01-27 19:30:45', '2025-01-27 19:25:00', '2025-01-27 19:30:45');

-- 插入线下支付测试数据
INSERT INTO `offline_payment` (`payment_id`, `merchant_id`, `merchant_name`, `order_no`, `pay_amount`, `pay_type`, `pay_status`, `trade_no`, `pay_time`, `notify_time`, `transfer_status`, `transfer_no`, `transfer_time`, `transfer_amount`, `platform_fee`, `create_time`, `update_time`, `remark`) VALUES
(3001, 1001, '张三便利店', 'OFF202501270001', 1500.00, '1', '1', '2025012722001234567890123462', '2025-01-27 07:45:20', '2025-01-27 07:45:25', '2', 'T202501270001', '2025-01-27 08:00:00', 1470.00, 30.00, '2025-01-27 07:40:00', '2025-01-27 08:00:00', '便利店收款'),
(3002, 1002, '李四餐厅', 'OFF202501270002', 2800.00, '2', '1', '4200001234567890123456789015', '2025-01-27 11:20:35', '2025-01-27 11:20:40', '2', 'T202501270002', '2025-01-27 11:35:00', 2744.00, 56.00, '2025-01-27 11:15:00', '2025-01-27 11:35:00', '餐厅收款'),
(3003, 1003, '王五超市', 'OFF202501270003', 3200.00, '1', '1', '2025012722001234567890123463', '2025-01-27 13:10:15', '2025-01-27 13:10:20', '1', '', NULL, 0.00, 0.00, '2025-01-27 13:05:00', '2025-01-27 13:10:20', '超市收款'),
(3004, 1004, '赵六药店', 'OFF202501270004', 850.00, '1', '1', '2025012722001234567890123464', '2025-01-27 15:25:50', '2025-01-27 15:25:55', '2', 'T202501270003', '2025-01-27 15:40:00', 833.00, 17.00, '2025-01-27 15:20:00', '2025-01-27 15:40:00', '药店收款'),
(3005, 1005, '钱七服装店', 'OFF202501270005', 1200.00, '2', '0', '', NULL, NULL, '0', '', NULL, 0.00, 0.00, '2025-01-27 16:30:00', '2025-01-27 16:30:00', '服装店收款'),
(3006, 1006, '孙八书店', 'OFF202501270006', 680.00, '1', '2', '', NULL, NULL, '0', '', NULL, 0.00, 0.00, '2025-01-27 17:15:00', '2025-01-27 17:15:00', '书店收款');

-- 插入支付回调日志测试数据
INSERT INTO `payment_callback_log` (`callback_id`, `order_no`, `business_type`, `pay_type`, `pay_amount`, `callback_status`, `callback_params`, `callback_time`, `retry_count`, `error_msg`, `create_time`, `update_time`) VALUES
(4001, 'TASK202501270001', 'task', '1', 50.00, '1', '{"out_trade_no":"TASK202501270001","trade_status":"TRADE_SUCCESS","total_amount":"50.00","trade_no":"2025012722001234567890123456"}', '2025-01-27 09:15:35', 0, NULL, '2025-01-27 09:15:35', '2025-01-27 09:15:35'),
(4002, 'TASK202501270002', 'task', '2', 30.00, '1', '{"out_trade_no":"TASK202501270002","trade_status":"TRADE_SUCCESS","total_amount":"30.00","trade_no":"4200001234567890123456789012"}', '2025-01-27 10:22:20', 0, NULL, '2025-01-27 10:22:20', '2025-01-27 10:22:20'),
(4003, 'TASK202501270006', 'task', '2', 40.00, '0', '{"out_trade_no":"TASK202501270006","trade_status":"TRADE_CLOSED","total_amount":"40.00"}', '2025-01-27 16:45:30', 2, '支付超时', '2025-01-27 16:45:30', '2025-01-27 16:50:15'),
(4004, 'MALL202501270001', 'mall', '1', 299.00, '1', '{"out_trade_no":"MALL202501270001","trade_status":"TRADE_SUCCESS","total_amount":"299.00","trade_no":"2025012722001234567890123459"}', '2025-01-27 08:30:20', 0, NULL, '2025-01-27 08:30:20', '2025-01-27 08:30:20'),
(4005, 'MALL202501270002', 'mall', '2', 199.00, '1', '{"out_trade_no":"MALL202501270002","trade_status":"TRADE_SUCCESS","total_amount":"199.00","trade_no":"4200001234567890123456789013"}', '2025-01-27 09:45:35', 0, NULL, '2025-01-27 09:45:35', '2025-01-27 09:45:35'),
(4006, 'MALL202501270006', 'mall', '2', 799.00, '0', '{"out_trade_no":"MALL202501270006","trade_status":"TRADE_CLOSED","total_amount":"799.00"}', '2025-01-27 15:45:25', 1, '签名验证失败', '2025-01-27 15:45:25', '2025-01-27 15:47:10'),
(4007, 'OFF202501270001', 'offline', '1', 1500.00, '1', '{"out_trade_no":"OFF202501270001","trade_status":"TRADE_SUCCESS","total_amount":"1500.00","trade_no":"2025012722001234567890123462"}', '2025-01-27 07:45:25', 0, NULL, '2025-01-27 07:45:25', '2025-01-27 07:45:25'),
(4008, 'OFF202501270002', 'offline', '2', 2800.00, '1', '{"out_trade_no":"OFF202501270002","trade_status":"TRADE_SUCCESS","total_amount":"2800.00","trade_no":"4200001234567890123456789015"}', '2025-01-27 11:20:40', 0, NULL, '2025-01-27 11:20:40', '2025-01-27 11:20:40'),
(4009, 'OFF202501270006', 'offline', '1', 680.00, '0', '{"out_trade_no":"OFF202501270006","trade_status":"TRADE_CLOSED","total_amount":"680.00"}', '2025-01-27 17:15:30', 3, '网络超时', '2025-01-27 17:15:30', '2025-01-27 17:20:45'),
(4010, 'TASK202501270007', 'task', '1', 200.00, '1', '{"out_trade_no":"TASK202501270007","trade_status":"TRADE_SUCCESS","total_amount":"200.00","trade_no":"2025012722001234567890123458"}', '2025-01-27 17:20:35', 0, NULL, '2025-01-27 17:20:35', '2025-01-27 17:20:35'),
(4011, 'MALL202501270007', 'mall', '1', 159.00, '1', '{"out_trade_no":"MALL202501270007","trade_status":"TRADE_SUCCESS","total_amount":"159.00","trade_no":"2025012722001234567890123461"}', '2025-01-27 16:10:30', 0, NULL, '2025-01-27 16:10:30', '2025-01-27 16:10:30'),
(4012, 'MALL202501270008', 'mall', '2', 259.00, '1', '{"out_trade_no":"MALL202501270008","trade_status":"TRADE_SUCCESS","total_amount":"259.00","trade_no":"4200001234567890123456789014"}', '2025-01-27 19:30:45', 0, NULL, '2025-01-27 19:30:45', '2025-01-27 19:30:45'),
(4013, 'TASK202501270003', 'task', '1', 80.00, '1', '{"out_trade_no":"TASK202501270003","trade_status":"TRADE_SUCCESS","total_amount":"80.00","trade_no":"2025012722001234567890123457"}', '2025-01-27 11:30:50', 0, NULL, '2025-01-27 11:30:50', '2025-01-27 11:30:50'),
(4014, 'MALL202501270003', 'mall', '1', 599.00, '1', '{"out_trade_no":"MALL202501270003","trade_status":"TRADE_SUCCESS","total_amount":"599.00","trade_no":"2025012722001234567890123460"}', '2025-01-27 12:15:50', 0, NULL, '2025-01-27 12:15:50', '2025-01-27 12:15:50'),
(4015, 'OFF202501270004', 'offline', '1', 850.00, '1', '{"out_trade_no":"OFF202501270004","trade_status":"TRADE_SUCCESS","total_amount":"850.00","trade_no":"2025012722001234567890123464"}', '2025-01-27 15:25:55', 0, NULL, '2025-01-27 15:25:55', '2025-01-27 15:25:55');

-- 更新自增ID起始值
ALTER TABLE `task_payment` AUTO_INCREMENT = 1009;
ALTER TABLE `mall_payment` AUTO_INCREMENT = 2009;
ALTER TABLE `offline_payment` AUTO_INCREMENT = 3007;
ALTER TABLE `payment_callback_log` AUTO_INCREMENT = 4016;

-- 查询验证数据
SELECT '任务支付统计' as '表名', 
       COUNT(*) as '总数', 
       SUM(CASE WHEN pay_status = '1' THEN 1 ELSE 0 END) as '成功数',
       SUM(CASE WHEN pay_status = '0' THEN 1 ELSE 0 END) as '待支付数',
       SUM(CASE WHEN pay_status = '2' THEN 1 ELSE 0 END) as '失败数',
       SUM(pay_amount) as '总金额'
FROM task_payment
UNION ALL
SELECT '商城支付统计' as '表名', 
       COUNT(*) as '总数', 
       SUM(CASE WHEN pay_status = '1' THEN 1 ELSE 0 END) as '成功数',
       SUM(CASE WHEN pay_status = '0' THEN 1 ELSE 0 END) as '待支付数',
       SUM(CASE WHEN pay_status = '2' THEN 1 ELSE 0 END) as '失败数',
       SUM(pay_amount) as '总金额'
FROM mall_payment
UNION ALL
SELECT '线下支付统计' as '表名', 
       COUNT(*) as '总数', 
       SUM(CASE WHEN pay_status = '1' THEN 1 ELSE 0 END) as '成功数',
       SUM(CASE WHEN pay_status = '0' THEN 1 ELSE 0 END) as '待支付数',
       SUM(CASE WHEN pay_status = '2' THEN 1 ELSE 0 END) as '失败数',
       SUM(pay_amount) as '总金额'
FROM offline_payment
UNION ALL
SELECT '回调日志统计' as '表名', 
       COUNT(*) as '总数', 
       SUM(CASE WHEN callback_status = '1' THEN 1 ELSE 0 END) as '成功数',
       SUM(CASE WHEN callback_status = '0' THEN 1 ELSE 0 END) as '失败数',
       0 as '待支付数',
       SUM(pay_amount) as '总金额'
FROM payment_callback_log;
