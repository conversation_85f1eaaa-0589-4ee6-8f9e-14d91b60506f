# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.9.0
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /www/wwwroot/fugaung/java/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8888
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: local
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: qazxswedcvfrtgbnhyujmkil
  # 令牌有效期（默认30分钟）
  expireTime: 2880

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 支付宝配置
alipay:
  # 应用ID（请替换为实际的应用ID）
  appId: 2021005185670626
  # 商户私钥（请替换为实际的私钥）
  privateKey: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC3XCHTvafIaJN37pzjn3d+NTbql9K3P/sNdyTp/U1APCOSxgl9U6wooAKPYeB/euH7uqeJ5BoYH8zWpgu6f2aEfLSZqCliJxriEUC2nnetiluAM91+PbevXxsNwhfbtWzlOTQ743avvq2MdGbrwIv24NEAu9zSVxl21gVR3D++2vzilGEL8dj1D8SdhwMIA/jWSPaoRLc5IU/E3bYbiSOt09UZsAUAhQh0ywcbic2HTH25NXb5fliUa8COwJDtfeJc0r7LXGdxYNhMaxvPgM4e4RSowj70ExwoWZq01YM6pvrihIc8x7eyIOn68sHGrdO04EJfemvVu22qCV/to0KNAgMBAAECggEAdByeuzRFdI5snCz5IteP1tch8Iq2AB8+/rbtJFOoQL4UlNRdnASJZ6x0EpIblP6e7kaz44kT0SUKFP9PPAmDRPCneQpZMnOq/W+JeFt/U+DispulblTs5dwnCJ5Zd2M7EykXX7laJk5sHh50MmRa/bo9/+X3HyfS00MSWVVM9LEpHhrMdQggtnhs0T/7BjBo08rfUgMw/l3Mjwx48tU9NBul/bTurCeibbldsHirgfTLTT43ftbMCA/vHf2GmyEpOzJx7iKXWLtT/Db90u/9cDjfb022mya8mqkspHBSPpdBko5rzXO6j9u46hetBxNi2IFDj50PfbN/w7EkT5MlbQKBgQD4ys7T/MUtWVSC6ObPRt47vHr/EOgpJKsIOLvpt4D5O2fXxTVjlnCUKF/6o8WIVQ5pyR2GVuDNs7FfWBzIen3GvL2JdSCgrgSV/7Eg69nRV9lfhEcUYE+itMZ1jqJ4OMPFKC9B3gjo9UXs/IP662N18gHvkg/Pxi5GzWy0/Z4EbwKBgQC8rAn03UFS+z577qqstLWAkvAQ6eySUHPE9FfJDTvAwmwf6ivOh8qzJbyRw7evx0KHq+jbc8BVcmqgMc5MiboJkvIlSfeeDhaBzSSb42Ngx8GOGDrBT+hdHF2TZT+EiLdR3q2W99hKJ2o6n/CJgxveTstsNIT/d8/WPOpkIX0+wwKBgCtyl34T1Yde41BdRfmKwcKi3sPueuy8d5Xe+ooNPtvHkRHwYat7mTt2dZR6x0NQv0ygD6TnRSkHKYGiDJzCL3bmfR8lMkZ9PVXIqnE6XvadJdv1aMhZLW8XrNTYzOy71Qx4QRB6qwmr08NYMeA+/UaatdXVpyc8z9YTh9lvtQnHAoGBALV1JRJLJgOgPmVFkwMNvi7No3Qw92V1WRLJChEE2D44/3LmboFxWoNYPPdYbDb0Bsmjjg5aUlYb9+7gWBCGudVxbdBtRmjOFdl7KsV/Odof5Mk8Bm5b4xiCKoGTdDaMovtrljrHXk9bfzCpGNe4sDnsQHtuO6fUXKEo7ymkh+evAoGAb9+663EqBUstqGnXTHB5efY7XniOqcRkH75WT9yaOxdg/akrzrFKulgB/9OoZEqq60iBqMXPljTcDkYYM9DsOkIFN59ehouSBydvWh0AABqk0g3Cor9XDBcU7Q1Rv7NS1ij2f0gTI8bCWf5KJGcDu7TQCC/y4xrXc4MW+0tseFU=
  # 支付宝公钥（请替换为实际的公钥）
  publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt1wh072nyGiTd+6c4593fjU26pfStz/7DXck6f1NQDwjksYJfVOsKKACj2Hgf3rh+7qnieQaGB/M1qYLun9mhHy0magpYica4hFAtp53rYpbgDPdfj23r18bDcIX27Vs5Tk0O+N2r76tjHRm68CL9uDRALvc0lcZdtYFUdw/vtr84pRhC/HY9Q/EnYcDCAP41kj2qES3OSFPxN22G4kjrdPVGbAFAIUIdMsHG4nNh0x9uTV2+X5YlGvAjsCQ7X3iXNK+y1xncWDYTGsbz4DOHuEUqMI+9BMcKFmatNWDOqb64oSHPMe3siDp+vLBxq3TtOBCX3pr1bttqglf7aNCjQIDAQAB
  # 服务器异步通知页面路径
  notifyUrl: https://web.hbfgbl.com/payment/alipay/notify
  # 页面跳转同步通知页面路径
  returnUrl: http://your-domain.com/app/payment/alipay/return
  # 签名方式
  signType: RSA2
  # 字符编码格式
  charset: UTF-8
  # 支付宝网关（沙箱环境）
  gatewayUrl: https://openapi.alipaydev.com/gateway.do
  # 是否使用证书模式（true=证书模式，false=公钥模式）
  useCert: true
  # 应用公钥证书路径（证书模式时必填）
  appCertPath: /Users/<USER>/浮光壁垒/支付证书/appCertPublicKey_2021005185670626.crt
  # 支付宝公钥证书路径（证书模式时必填）
  alipayCertPath: /Users/<USER>/浮光壁垒/支付证书/alipayCertPublicKey_RSA2.crt
  # 支付宝根证书路径（证书模式时必填）
  alipayRootCertPath: /Users/<USER>/浮光壁垒/支付证书/alipayRootCert.crt

# 阿里云短信服务配置
aliyun:
  sms:
    # 访问密钥ID
    access-key-id: LTAI5tEt7jwdHagEmykz46PK
    # 访问密钥Secret
    access-key-secret: ******************************
    # 短信签名
    sign-name: 浮光壁垒
    # 验证码模板代码
    template-code: SMS_324422613
    # 短信发送区域
    region-id: cn-hangzhou
