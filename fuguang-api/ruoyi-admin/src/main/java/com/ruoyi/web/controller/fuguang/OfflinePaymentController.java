package com.ruoyi.web.controller.fuguang;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.fuguang.domain.OfflinePayment;
import com.ruoyi.fuguang.service.IOfflinePaymentService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 线下支付订单Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/fuguang/offlinePayment")
public class OfflinePaymentController extends BaseController
{
    @Autowired
    private IOfflinePaymentService offlinePaymentService;

    /**
     * 查询线下支付订单列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:list')")
    @GetMapping("/list")
    public TableDataInfo list(OfflinePayment offlinePayment)
    {
        startPage();
        List<OfflinePayment> list = offlinePaymentService.selectOfflinePaymentList(offlinePayment);
        return getDataTable(list);
    }

    /**
     * 导出线下支付订单列表
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:export')")
    @Log(title = "线下支付订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OfflinePayment offlinePayment)
    {
        List<OfflinePayment> list = offlinePaymentService.selectOfflinePaymentList(offlinePayment);
        ExcelUtil<OfflinePayment> util = new ExcelUtil<OfflinePayment>(OfflinePayment.class);
        util.exportExcel(response, list, "线下支付订单数据");
    }

    /**
     * 获取线下支付订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:query')")
    @GetMapping(value = "/{paymentId}")
    public AjaxResult getInfo(@PathVariable("paymentId") Long paymentId)
    {
        return success(offlinePaymentService.selectOfflinePaymentByPaymentId(paymentId));
    }

    /**
     * 新增线下支付订单
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:add')")
    @Log(title = "线下支付订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OfflinePayment offlinePayment)
    {
        return toAjax(offlinePaymentService.insertOfflinePayment(offlinePayment));
    }

    /**
     * 修改线下支付订单
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:edit')")
    @Log(title = "线下支付订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OfflinePayment offlinePayment)
    {
        return toAjax(offlinePaymentService.updateOfflinePayment(offlinePayment));
    }

    /**
     * 删除线下支付订单
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:remove')")
    @Log(title = "线下支付订单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{paymentIds}")
    public AjaxResult remove(@PathVariable Long[] paymentIds)
    {
        return toAjax(offlinePaymentService.deleteOfflinePaymentByPaymentIds(paymentIds));
    }

    /**
     * 重新转账给商家
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:transfer')")
    @Log(title = "线下支付转账", businessType = BusinessType.UPDATE)
    @PostMapping("/transfer/{paymentId}")
    public AjaxResult retryTransfer(@PathVariable Long paymentId)
    {
       return offlinePaymentService.transferToMerchant(paymentId);
    }

    /**
     * 根据订单号查询支付订单
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:query')")
    @GetMapping("/orderNo/{orderNo}")
    public AjaxResult getByOrderNo(@PathVariable String orderNo)
    {
        return success(offlinePaymentService.selectOfflinePaymentByOrderNo(orderNo));
    }

    /**
     * 获取支付统计信息
     */
    @PreAuthorize("@ss.hasPermi('fuguang:offlinePayment:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        // 查询各种状态的订单数量和金额
        OfflinePayment query = new OfflinePayment();

        // 待支付订单
        query.setPayStatus("0");
        List<OfflinePayment> pendingList = offlinePaymentService.selectOfflinePaymentList(query);
        int pendingCount = pendingList.size();
        BigDecimal pendingAmount = pendingList.stream()
            .map(OfflinePayment::getPayAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 支付成功订单
        query = new OfflinePayment();
        query.setPayStatus("1");
        List<OfflinePayment> successList = offlinePaymentService.selectOfflinePaymentList(query);
        int successCount = successList.size();
        BigDecimal successAmount = successList.stream()
            .map(OfflinePayment::getPayAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 支付失败订单
        query = new OfflinePayment();
        query.setPayStatus("2");
        List<OfflinePayment> failedList = offlinePaymentService.selectOfflinePaymentList(query);
        int failedCount = failedList.size();
        BigDecimal failedAmount = failedList.stream()
            .map(OfflinePayment::getPayAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 转账失败订单
        query = new OfflinePayment();
        query.setPayStatus("1");
        query.setTransferStatus("3");
        List<OfflinePayment> transferFailedList = offlinePaymentService.selectOfflinePaymentList(query);
        int transferFailedCount = transferFailedList.size();
        BigDecimal transferFailedAmount = transferFailedList.stream()
            .map(OfflinePayment::getPayAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 总订单
        query = new OfflinePayment();
        List<OfflinePayment> totalList = offlinePaymentService.selectOfflinePaymentList(query);
        int totalCount = totalList.size();
        BigDecimal totalAmount = totalList.stream()
            .map(OfflinePayment::getPayAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 构建统计结果
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("pendingPayments", pendingCount);
        statistics.put("pendingAmount", pendingAmount);
        statistics.put("successPayments", successCount);
        statistics.put("successAmount", successAmount);
        statistics.put("failedPayments", failedCount);
        statistics.put("failedAmount", failedAmount);
        statistics.put("transferFailedPayments", transferFailedCount);
        statistics.put("transferFailedAmount", transferFailedAmount);
        statistics.put("totalPayments", totalCount);
        statistics.put("totalAmount", totalAmount);

        return success(statistics);
    }
}
