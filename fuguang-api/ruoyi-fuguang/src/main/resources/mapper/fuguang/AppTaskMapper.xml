<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.AppTaskMapper">
    
    <resultMap type="AppTask" id="AppTaskResult">
        <id     property="taskId"           column="task_id"         />
        <result property="taskTitle"        column="task_title"      />
        <result property="taskDesc"         column="task_desc"       />
        <result property="taskAmount"       column="task_amount"     />
        <result property="taskStatus"       column="task_status"     />
        <result property="taskType"         column="task_type"       />
        <result property="firstTypeId"      column="first_type_id"   />
        <result property="secondTypeId"     column="second_type_id"  />
        <result property="urgentLevel"      column="urgent_level"    />
        <result property="publisherId"      column="publisher_id"    />
        <result property="publisherName"    column="publisher_name"  />
        <result property="publisherAvatar"  column="publisher_avatar"/>
        <result property="receiverId"       column="receiver_id"     />
        <result property="receiverName"     column="receiver_name"   />
        <result property="taskAddress"      column="task_address"    />
        <result property="longitude"        column="longitude"       />
        <result property="latitude"         column="latitude"        />
        <result property="startTime"        column="start_time"      />
        <result property="endTime"          column="end_time"        />
        <result property="viewCount"        column="view_count"      />
        <result property="hotScore"         column="hot_score"       />
        <result property="createBy"         column="create_by"       />
        <result property="createTime"       column="create_time"     />
        <result property="updateBy"         column="update_by"       />
        <result property="updateTime"       column="update_time"     />
        <result property="remark"           column="remark"          />
        <result property="maintenanceFeeProportion"           column="maintenance_fee_proportion"          />
        <result property="guaranteeProportion"           column="guarantee_proportion"          />
    </resultMap>

    <!-- 带任务类型名称的结果映射 -->
    <resultMap type="AppTask" id="AppTaskWithTypeNameResult" extends="AppTaskResult">
        <result property="firstTypeName"    column="first_type_name" />
        <result property="secondTypeName"   column="second_type_name"/>
    </resultMap>

    <sql id="selectAppTaskVo">
        select task_id, maintenance_fee_proportion,guarantee_proportion,task_title, task_desc, task_amount, task_status, task_type, first_type_id, second_type_id, urgent_level, publisher_id, publisher_name, publisher_avatar, receiver_id, receiver_name, task_address, longitude, latitude, start_time, end_time, view_count, hot_score, create_by, create_time, update_by, update_time, remark from app_task
    </sql>

    <select id="selectAppTaskList" parameterType="AppTask" resultMap="AppTaskResult">
        <include refid="selectAppTaskVo"/>
        <where>  
            <if test="taskTitle != null  and taskTitle != ''"> and task_title like concat('%', #{taskTitle}, '%')</if>
            <if test="taskStatus != null  and taskStatus != ''"> and task_status = #{taskStatus}</if>
            <if test="taskType != null  and taskType != ''"> and task_type = #{taskType}</if>
            <if test="publisherId != null "> and publisher_id = #{publisherId}</if>
            <if test="receiverId != null "> and receiver_id = #{receiverId}</if>
            <if test="taskAddress != null  and taskAddress != ''"> and task_address like concat('%', #{taskAddress}, '%')</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectAppTaskByTaskId" parameterType="Long" resultMap="AppTaskWithTypeNameResult">
        select t.task_id, t.task_title, t.task_desc, t.task_amount, t.task_status, t.task_type,
               t.first_type_id, t.second_type_id, t.urgent_level, t.publisher_id, t.publisher_name,
               t.publisher_avatar, t.receiver_id, t.receiver_name, t.task_address, t.longitude,
               t.latitude, t.start_time, t.end_time, t.view_count, t.hot_score, t.create_by,
               t.create_time, t.update_by, t.update_time, t.remark,t.guarantee_proportion,t.maintenance_fee_proportion,
               ft.type_name as first_type_name,
               st.type_name as second_type_name
        from app_task t
        left join app_task_type ft on t.first_type_id = ft.type_id
        left join app_task_type st on t.second_type_id = st.type_id
        where t.task_id = #{taskId}
    </select>
    
    <select id="selectHotTaskList" resultMap="AppTaskResult">
        <include refid="selectAppTaskVo"/>
        where task_status = '0'
        order by hot_score desc, create_time desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>
    
    <select id="selectTasksByPublisher" parameterType="Long" resultMap="AppTaskResult">
        <include refid="selectAppTaskVo"/>
        where publisher_id = #{userId}
        order by create_time desc
    </select>
    
    <select id="selectTasksByReceiver" parameterType="Long" resultMap="AppTaskResult">
        <include refid="selectAppTaskVo"/>
        where receiver_id = #{userId}
        order by create_time desc
    </select>
        
    <insert id="insertAppTask" parameterType="AppTask" useGeneratedKeys="true" keyProperty="taskId">
        insert into app_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskTitle != null and taskTitle != ''">task_title,</if>
            <if test="taskDesc != null">task_desc,</if>
            <if test="taskAmount != null">task_amount,</if>
            <if test="taskStatus != null">task_status,</if>
            <if test="taskType != null">task_type,</if>
            <if test="firstTypeId != null">first_type_id,</if>
            <if test="secondTypeId != null">second_type_id,</if>
            <if test="urgentLevel != null">urgent_level,</if>
            <if test="publisherId != null">publisher_id,</if>
            <if test="publisherName != null">publisher_name,</if>
            <if test="publisherAvatar != null">publisher_avatar,</if>
            <if test="receiverId != null">receiver_id,</if>
            <if test="receiverName != null">receiver_name,</if>
            <if test="taskAddress != null">task_address,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="hotScore != null">hot_score,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="guaranteeProportion != null">guarantee_proportion,</if>
            <if test="maintenanceFeeProportion != null">maintenance_fee_proportion,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskTitle != null and taskTitle != ''">#{taskTitle},</if>
            <if test="taskDesc != null">#{taskDesc},</if>
            <if test="taskAmount != null">#{taskAmount},</if>
            <if test="taskStatus != null">#{taskStatus},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="firstTypeId != null">#{firstTypeId},</if>
            <if test="secondTypeId != null">#{secondTypeId},</if>
            <if test="urgentLevel != null">#{urgentLevel},</if>
            <if test="publisherId != null">#{publisherId},</if>
            <if test="publisherName != null">#{publisherName},</if>
            <if test="publisherAvatar != null">#{publisherAvatar},</if>
            <if test="receiverId != null">#{receiverId},</if>
            <if test="receiverName != null">#{receiverName},</if>
            <if test="taskAddress != null">#{taskAddress},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="hotScore != null">#{hotScore},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="guaranteeProportion != null">#{guaranteeProportion},</if>
            <if test="maintenanceFeeProportion != null">#{maintenanceFeeProportion},</if>
         </trim>
    </insert>

    <update id="updateAppTask" parameterType="AppTask">
        update app_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskTitle != null and taskTitle != ''">task_title = #{taskTitle},</if>
            <if test="taskDesc != null">task_desc = #{taskDesc},</if>
            <if test="taskAmount != null">task_amount = #{taskAmount},</if>
            <if test="taskStatus != null">task_status = #{taskStatus},</if>
            <if test="taskType != null">task_type = #{taskType},</if>
            <if test="firstTypeId != null">first_type_id = #{firstTypeId},</if>
            <if test="secondTypeId != null">second_type_id = #{secondTypeId},</if>
            <if test="urgentLevel != null">urgent_level = #{urgentLevel},</if>
            <if test="publisherId != null">publisher_id = #{publisherId},</if>
            <if test="publisherName != null">publisher_name = #{publisherName},</if>
            <if test="publisherAvatar != null">publisher_avatar = #{publisherAvatar},</if>
            <if test="receiverId != null">receiver_id = #{receiverId},</if>
            <if test="receiverName != null">receiver_name = #{receiverName},</if>
            <if test="taskAddress != null">task_address = #{taskAddress},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="hotScore != null">hot_score = #{hotScore},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteAppTaskByTaskId" parameterType="Long">
        delete from app_task where task_id = #{taskId}
    </delete>

    <delete id="deleteAppTaskByTaskIds" parameterType="String">
        delete from app_task where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
    
    <update id="increaseViewCount" parameterType="Long">
        update app_task set view_count = view_count + 1 where task_id = #{taskId}
    </update>
    
    <update id="acceptTask">
        update app_task set 
            task_status = '1',
            receiver_id = #{userId},
            receiver_name = #{userName},
            update_time = now()
        where task_id = #{taskId} and task_status = '0'
    </update>
    
    <update id="completeTask" parameterType="Long">
        update app_task set 
            task_status = '2',
            update_time = now()
        where task_id = #{taskId}
    </update>
    
    <update id="cancelTask" parameterType="Long">
        update app_task set 
            task_status = '3',
            update_time = now()
        where task_id = #{taskId}
    </update>

</mapper>
