package com.ruoyi.fuguang.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fuguang.config.AlipayConfig;
import com.ruoyi.fuguang.domain.AppTask;
import com.ruoyi.fuguang.service.IAlipayService;
import com.ruoyi.fuguang.service.IAppTaskService;
import com.ruoyi.fuguang.service.IAppUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.fuguang.mapper.TaskPaymentMapper;
import com.ruoyi.fuguang.domain.TaskPayment;
import com.ruoyi.fuguang.service.ITaskPaymentService;

/**
 * 任务支付记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class TaskPaymentServiceImpl implements ITaskPaymentService 
{
    private static final Logger log = LoggerFactory.getLogger(TaskPaymentServiceImpl.class);

    @Autowired
    private TaskPaymentMapper taskPaymentMapper;

    @Autowired
    private IAlipayService alipayService;


    /**
     * 查询任务支付记录
     * 
     * @param paymentId 任务支付记录主键
     * @return 任务支付记录
     */
    @Override
    public TaskPayment selectTaskPaymentByPaymentId(Long paymentId)
    {
        return taskPaymentMapper.selectTaskPaymentByPaymentId(paymentId);
    }

    /**
     * 查询任务支付记录列表
     * 
     * @param taskPayment 任务支付记录
     * @return 任务支付记录
     */
    @Override
    public List<TaskPayment> selectTaskPaymentList(TaskPayment taskPayment)
    {
        return taskPaymentMapper.selectTaskPaymentList(taskPayment);
    }

    /**
     * 新增任务支付记录
     * 
     * @param taskPayment 任务支付记录
     * @return 结果
     */
    @Override
    public int insertTaskPayment(TaskPayment taskPayment)
    {
        taskPayment.setCreateBy(SecurityUtils.getUsername());
        taskPayment.setCreateTime(DateUtils.getNowDate());
        return taskPaymentMapper.insertTaskPayment(taskPayment);
    }

    /**
     * 修改任务支付记录
     * 
     * @param taskPayment 任务支付记录
     * @return 结果
     */
    @Override
    public int updateTaskPayment(TaskPayment taskPayment)
    {
        taskPayment.setUpdateTime(DateUtils.getNowDate());
        return taskPaymentMapper.updateTaskPayment(taskPayment);
    }

    /**
     * 批量删除任务支付记录
     * 
     * @param paymentIds 需要删除的任务支付记录主键
     * @return 结果
     */
    @Override
    public int deleteTaskPaymentByPaymentIds(Long[] paymentIds)
    {
        return taskPaymentMapper.deleteTaskPaymentByPaymentIds(paymentIds);
    }

    /**
     * 删除任务支付记录信息
     * 
     * @param paymentId 任务支付记录主键
     * @return 结果
     */
    @Override
    public int deleteTaskPaymentByPaymentId(Long paymentId)
    {
        return taskPaymentMapper.deleteTaskPaymentByPaymentId(paymentId);
    }

    /**
     * 根据任务ID查询支付记录
     * 
     * @param taskId 任务ID
     * @return 任务支付记录
     */
    @Override
    public TaskPayment selectTaskPaymentByTaskId(Long taskId)
    {
        return taskPaymentMapper.selectTaskPaymentByTaskId(taskId);
    }

    /**
     * 根据订单号查询支付记录
     * 
     * @param orderNo 订单号
     * @return 任务支付记录
     */
    @Override
    public TaskPayment selectTaskPaymentByOrderNo(String orderNo)
    {
        return taskPaymentMapper.selectTaskPaymentByOrderNo(orderNo);
    }

    /**
     * 创建任务支付订单
     * 
     * @param task 任务信息
     * @param payType 支付方式
     * @return 支付参数
     */
    @Override
    @Transactional
    public TaskPayment createTaskPayOrder(AppTask task, String payType)
    {
        try {
            // 检查是否已存在支付记录
            TaskPayment existPayment = selectTaskPaymentByTaskId(task.getTaskId());
            if (StringUtils.isNotNull(existPayment)) {
                if(!existPayment.getPayStatus().equals("0")){
                    throw new ServiceException("订单无法发起支付");
                }
                return existPayment;
            }
            // 生成订单号
            String orderNo = "TASK_" + System.currentTimeMillis() + "_" + task.getTaskId();
            // 创建支付记录
            TaskPayment payment = new TaskPayment();
            payment.setTaskId(task.getTaskId());
            payment.setTaskTitle(task.getTaskTitle());
            payment.setUserId(task.getPublisherId());
            payment.setUserName(task.getPublisherName());
            payment.setOrderNo(orderNo);
            payment.setPayAmount(task.getTaskAmount());
            payment.setPayType(payType);
            payment.setPayStatus("0"); // 待支付
            // 如果存在未支付记录，先删除
            if (existPayment != null && "0".equals(existPayment.getPayStatus())) {
                deleteTaskPaymentByPaymentId(existPayment.getPaymentId());
            }
            // 根据支付方式创建支付订单
            switch (payType){
                case "1":
                    // 支付宝支付
                    Map<String, Object> payResult = alipayService.createAppPayOrder(
                            orderNo,
                            task.getTaskAmount(),
                            "任务发布-" + task.getTaskTitle(),
                            "任务发布支付，任务ID：" + task.getTaskId()
                    );
                    if ((Boolean) payResult.get("success")) {
                        payment.setPayOrderString((String) payResult.get("orderString"));
                        break;
                    } else {
                        throw new ServiceException("创建支付订单失败：" + payResult.get("errorMsg"));
                    }
                case "2":
                case "3":
                    throw new ServiceException("暂未开放");
                default:
                        throw new ServiceException("暂不支持");
            }
            insertTaskPayment(payment);
            return payment;
        } catch (Exception e) {
            log.error("创建任务支付订单异常，任务ID：{}", task.getTaskId(), e);
            throw new RuntimeException("创建支付订单异常：" + e.getMessage());
        }
    }

    /**
     * 处理支付宝支付回调
     * 
     * @param params 回调参数
     * @return 处理结果
     */
    @Override
    @Transactional
    public boolean handleAlipayCallback(Map<String, String> params)
    {
        try {
            // 验证签名
            if (!alipayService.verifyCallback(params)) {
                log.error("支付宝回调签名验证失败");
                return false;
            }
            
            String tradeStatus = params.get("trade_status");
            String outTradeNo = params.get("out_trade_no");
            String tradeNo = params.get("trade_no");
            
            if ("TRADE_SUCCESS".equals(tradeStatus) || "TRADE_FINISHED".equals(tradeStatus)) {
                // 支付成功
                return paymentSuccess(outTradeNo, tradeNo, "1") > 0;
            } else {
                log.warn("支付宝回调状态异常：{}", tradeStatus);
                return false;
            }
        } catch (Exception e) {
            log.error("处理支付宝回调异常", e);
            return false;
        }
    }

    /**
     * 查询支付状态
     * 
     * @param orderNo 订单号
     * @return 支付状态
     */
    @Override
    public String getPaymentStatus(String orderNo)
    {
        TaskPayment payment = selectTaskPaymentByOrderNo(orderNo);
        return payment != null ? payment.getPayStatus() : "0";
    }

    /**
     * 支付成功处理
     * 
     * @param orderNo 订单号
     * @param tradeNo 第三方交易号
     * @param payType 支付方式
     * @return 结果
     */
    @Override
    @Transactional
    public int paymentSuccess(String orderNo, String tradeNo, String payType)
    {
        try {
            // 更新支付记录
            int result = taskPaymentMapper.updatePaymentStatusByOrderNo(orderNo, "1", tradeNo);
            
            if (result > 0) {
                // 获取支付记录
                TaskPayment payment = selectTaskPaymentByOrderNo(orderNo);
                if (payment != null) {
                    // 更新任务状态为已发布（如果需要的话，这里可以添加相关逻辑）
                    log.info("任务支付成功，任务ID：{}，订单号：{}", payment.getTaskId(), orderNo);
                }
            }
            
            return result;
        } catch (Exception e) {
            log.error("支付成功处理异常，订单号：{}", orderNo, e);
            return 0;
        }
    }

    /**
     * 根据用户ID查询支付记录列表
     * 
     * @param userId 用户ID
     * @return 任务支付记录集合
     */
    @Override
    public List<TaskPayment> selectTaskPaymentListByUserId(Long userId)
    {
        return taskPaymentMapper.selectTaskPaymentListByUserId(userId);
    }
}
