package com.ruoyi.fuguang.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.fuguang.domain.OfflinePayment;
import com.ruoyi.fuguang.domain.MerchantQrcode;

/**
 * 线下支付Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface IOfflinePaymentService 
{
    /**
     * 查询线下支付订单
     * 
     * @param paymentId 线下支付订单主键
     * @return 线下支付订单
     */
    public OfflinePayment selectOfflinePaymentByPaymentId(Long paymentId);

    /**
     * 根据订单号查询线下支付订单
     * 
     * @param orderNo 订单号
     * @return 线下支付订单
     */
    public OfflinePayment selectOfflinePaymentByOrderNo(String orderNo);

    /**
     * 查询线下支付订单列表
     * 
     * @param offlinePayment 线下支付订单
     * @return 线下支付订单集合
     */
    public List<OfflinePayment> selectOfflinePaymentList(OfflinePayment offlinePayment);

    /**
     * 新增线下支付订单
     * 
     * @param offlinePayment 线下支付订单
     * @return 结果
     */
    public int insertOfflinePayment(OfflinePayment offlinePayment);

    /**
     * 修改线下支付订单
     * 
     * @param offlinePayment 线下支付订单
     * @return 结果
     */
    public int updateOfflinePayment(OfflinePayment offlinePayment);

    /**
     * 批量删除线下支付订单
     * 
     * @param paymentIds 需要删除的线下支付订单主键集合
     * @return 结果
     */
    public int deleteOfflinePaymentByPaymentIds(Long[] paymentIds);

    /**
     * 删除线下支付订单信息
     * 
     * @param paymentId 线下支付订单主键
     * @return 结果
     */
    public int deleteOfflinePaymentByPaymentId(Long paymentId);

    /**
     * 生成商家二维码
     * 
     * @param userId 用户主键
     * @param offerDiscounts 让利比例
     * @return 二维码信息
     */
    public AjaxResult generateMerchantQrcode(Long userId,BigDecimal offerDiscounts);

    /**
     * 获取商家二维码
     * 
     * @return 二维码信息
     */
    public List<MerchantQrcode> getMerchantQrcode(Long userId);

    /**
     * 创建线下支付订单
     * 
     * @param qrcodeId 二维码ID
     * @param payAmount 支付金额
     * @return 支付订单信息
     */
    public AjaxResult createOfflinePayOrder(String qrcodeId, BigDecimal payAmount);

    /**
     * 处理支付宝支付回调
     * 
     * @param params 回调参数
     * @return 处理结果
     */
    public boolean handleAlipayCallback(Map<String, String> params);


    /**
     * 转账给商家
     * 
     * @param paymentId 支付记录ID
     * @return 转账结果
     */
    public AjaxResult transferToMerchant(Long paymentId);

    /**
     * 查询支付状态
     * 
     * @param orderNo 订单号
     * @return 支付状态
     */
    public String getPaymentStatus(String orderNo);
}
