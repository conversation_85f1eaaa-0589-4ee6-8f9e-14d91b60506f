package com.ruoyi.fuguang.service.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.fuguang.domain.AppUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.fuguang.domain.MerchantApplication;
import com.ruoyi.fuguang.domain.MerchantQrcode;
import com.ruoyi.fuguang.domain.OfflinePayment;
import com.ruoyi.fuguang.mapper.MerchantQrcodeMapper;
import com.ruoyi.fuguang.mapper.OfflinePaymentMapper;
import com.ruoyi.fuguang.service.IAlipayService;
import com.ruoyi.fuguang.service.IMerchantApplicationService;
import com.ruoyi.fuguang.service.IOfflinePaymentService;

/**
 * 线下支付Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Service
public class OfflinePaymentServiceImpl implements IOfflinePaymentService 
{
    private static final Logger log = LoggerFactory.getLogger(OfflinePaymentServiceImpl.class);

    @Autowired
    private OfflinePaymentMapper offlinePaymentMapper;

    @Autowired
    private MerchantQrcodeMapper merchantQrcodeMapper;

    @Autowired
    private IMerchantApplicationService merchantApplicationService;

    @Autowired
    private IAlipayService alipayService;

    @Autowired
    private AppUserServiceImpl appUserServiceImpl;

    /**
     * 查询线下支付订单
     * 
     * @param paymentId 线下支付订单主键
     * @return 线下支付订单
     */
    @Override
    public OfflinePayment selectOfflinePaymentByPaymentId(Long paymentId)
    {
        return offlinePaymentMapper.selectOfflinePaymentByPaymentId(paymentId);
    }

    /**
     * 根据订单号查询线下支付订单
     * 
     * @param orderNo 订单号
     * @return 线下支付订单
     */
    @Override
    public OfflinePayment selectOfflinePaymentByOrderNo(String orderNo)
    {
        return offlinePaymentMapper.selectOfflinePaymentByOrderNo(orderNo);
    }

    /**
     * 查询线下支付订单列表
     * 
     * @param offlinePayment 线下支付订单
     * @return 线下支付订单
     */
    @Override
    public List<OfflinePayment> selectOfflinePaymentList(OfflinePayment offlinePayment)
    {
        return offlinePaymentMapper.selectOfflinePaymentList(offlinePayment);
    }

    /**
     * 新增线下支付订单
     * 
     * @param offlinePayment 线下支付订单
     * @return 结果
     */
    @Override
    public int insertOfflinePayment(OfflinePayment offlinePayment)
    {
        offlinePayment.setCreateTime(DateUtils.getNowDate());
        return offlinePaymentMapper.insertOfflinePayment(offlinePayment);
    }

    /**
     * 修改线下支付订单
     * 
     * @param offlinePayment 线下支付订单
     * @return 结果
     */
    @Override
    public int updateOfflinePayment(OfflinePayment offlinePayment)
    {
        offlinePayment.setUpdateTime(DateUtils.getNowDate());
        return offlinePaymentMapper.updateOfflinePayment(offlinePayment);
    }

    /**
     * 批量删除线下支付订单
     * 
     * @param paymentIds 需要删除的线下支付订单主键
     * @return 结果
     */
    @Override
    public int deleteOfflinePaymentByPaymentIds(Long[] paymentIds)
    {
        return offlinePaymentMapper.deleteOfflinePaymentByPaymentIds(paymentIds);
    }

    /**
     * 删除线下支付订单信息
     * 
     * @param paymentId 线下支付订单主键
     * @return 结果
     */
    @Override
    public int deleteOfflinePaymentByPaymentId(Long paymentId)
    {
        return offlinePaymentMapper.deleteOfflinePaymentByPaymentId(paymentId);
    }

    /**
     * 生成商家二维码
     *
     * @param userId 用户主键
     * @param offerDiscounts 让利比例
     * @return 二维码信息
     */
    @Override
    public AjaxResult generateMerchantQrcode(Long userId,BigDecimal offerDiscounts)
    {
        try {
            // 查询商家信息
            MerchantApplication merchant = merchantApplicationService.selectMerchantApplicationByUserId(userId);
            if (merchant == null) {
                return AjaxResult.error("商家不存在");
            }
            if (!"1".equals(merchant.getApplicationStatus())) {
                return AjaxResult.error("商家未通过审核");
            }

            // 创建二维码记录
            MerchantQrcode qrcode = new MerchantQrcode();
            qrcode.setQrcodeId(IdUtils.fastSimpleUUID());
            qrcode.setMerchantId(merchant.getApplicationId());
            qrcode.setOfferDiscounts(offerDiscounts);
            qrcode.setMerchantName(merchant.getShopName());

            // 生成二维码内容（包含商家ID和基础URL）
            String baseUrl = "https://your-domain.com/app/offline-pay";
            String qrcodeContent = baseUrl + "?qrcodeId=" + qrcode.getQrcodeId();

            qrcode.setQrcodeContent(qrcodeContent);
            qrcode.setStatus("0");
            qrcode.setCreateTime(DateUtils.getNowDate());
            merchantQrcodeMapper.insertMerchantQrcode(qrcode);
            log.info("商家二维码生成成功，商家ID：{}", merchant.getApplicationId());
            return AjaxResult.success(qrcode);
        } catch (Exception e) {
            log.error("生成商家二维码异常:{}", e.getMessage());
            return  AjaxResult.error("生成二维码失败,请联系管理员");
        }
    }

    /**
     * 获取商家二维码
     * 
     * @param userId 用户主键
     * @return 二维码信息
     */
    @Override
    public List<MerchantQrcode> getMerchantQrcode(Long userId)
    {
        // 查询商家信息
        MerchantApplication merchant = merchantApplicationService.selectMerchantApplicationByUserId(userId);
        if (merchant == null) {
            return null;
        }
        MerchantQrcode  qrcode = new MerchantQrcode();
        qrcode.setStatus("0");
        qrcode.setMerchantId(merchant.getApplicationId());
        return merchantQrcodeMapper.selectMerchantQrcodeList(qrcode);
    }

    /**
     * 创建线下支付订单
     * 
     * @param qrcodeId 二维码ID
     * @param payAmount 支付金额
     * @return 支付订单信息
     */
    @Override
    @Transactional
    public AjaxResult createOfflinePayOrder(String qrcodeId, BigDecimal payAmount)
    {
        try {
            // 查询商家信息
            MerchantQrcode merchantQrcode = merchantQrcodeMapper.selectMerchantQrcodeByQrcodeId(qrcodeId);
            if (StringUtils.isNull(merchantQrcode)||merchantQrcode.getStatus().equals("1")) {
                return AjaxResult.error("二维码无效");
            }
            // 生成订单号
            String orderNo = "OFF" + System.currentTimeMillis() + String.format("%04d", (int)(Math.random() * 10000));
            
            // 创建线下支付订单
            OfflinePayment payment = new OfflinePayment();
            payment.setMerchantId(merchantQrcode.getMerchantId());
            payment.setMerchantName(merchantQrcode.getMerchantName());
            payment.setOrderNo(orderNo);
            payment.setPayAmount(payAmount);
            payment.setPayType("1"); // 支付宝
            payment.setPayStatus("0"); // 待支付
            payment.setTransferStatus("0"); // 未转账
            payment.setPlatformFee(payAmount.multiply(new BigDecimal("0.006"))); // 0.6%手续费
            payment.setAmountDiscounts(payAmount.multiply(merchantQrcode.getOfferDiscounts()));
            payment.setTransferAmount(payAmount.subtract(payment.getPlatformFee()).subtract(payment.getAmountDiscounts()));
            payment.setCreateTime(DateUtils.getNowDate());
            payment.setPayUserId(SecurityUtils.getUserId());
            insertOfflinePayment(payment);
            
            // 创建支付宝支付订单
            String subject = "线下支付-" + merchantQrcode.getMerchantName();
            String body = "商家：" + merchantQrcode.getMerchantName() + "，金额：" + payAmount + "元";

            Map<String, Object> alipayResult = alipayService.createAppPayOrder(orderNo, payAmount, subject, body);
            
            if ((Boolean) alipayResult.get("success")) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("orderNo", orderNo);
                result.put("payAmount", payAmount);
                result.put("merchantName", merchantQrcode.getMerchantName());
                result.put("orderString", alipayResult.get("orderString"));
                log.info("线下支付订单创建成功，订单号：{}", orderNo);
                return AjaxResult.success(result);
            } else {
                return AjaxResult.error("创建支付订单失败" + alipayResult.get("errorMsg"));
            }
        } catch (Exception e) {
            log.error("创建线下支付订单异常，二维码ID：{}，金额：{}", qrcodeId, payAmount, e);
            return AjaxResult.error("创建支付订单失败");
        }
    }

    /**
     * 处理支付宝支付回调
     *
     * @param params 回调参数
     * @return 处理结果
     */
    @Override
    @Transactional
    public boolean handleAlipayCallback(Map<String, String> params)
    {
        try {
            String orderNo = params.get("out_trade_no");
            String tradeNo = params.get("trade_no");
            String tradeStatus = params.get("trade_status");

            log.info("处理线下支付支付宝回调，订单号：{}，交易号：{}，状态：{}", orderNo, tradeNo, tradeStatus);

            // 验证签名
            boolean signVerified = alipayService.verifyCallback(params);
            if (!signVerified) {
                log.error("支付宝回调签名验证失败，订单号：{}", orderNo);
                return false;
            }

            // 查询支付订单
            OfflinePayment payment = selectOfflinePaymentByOrderNo(orderNo);
            if (payment == null) {
                log.error("线下支付订单不存在，订单号：{}", orderNo);
                return false;
            }

            // 处理支付成功
            if ("TRADE_SUCCESS".equals(tradeStatus) && "0".equals(payment.getPayStatus())) {
                payment.setPayStatus("1");
                payment.setTradeNo(tradeNo);
                payment.setPayTime(DateUtils.getNowDate());
                payment.setNotifyTime(DateUtils.getNowDate());
                updateOfflinePayment(payment);

                // 异步转账给商家
                transferToMerchant(payment.getPaymentId());

                log.info("线下支付订单支付成功，订单号：{}", orderNo);
                return true;
            }

            return true;
        } catch (Exception e) {
            log.error("处理线下支付支付宝回调异常", e);
            return false;
        }
    }
    /**
     * 转账给商家
     *
     * @param paymentId 支付记录ID
     * @return 转账结果
     */
    @Override
    @Transactional
    public AjaxResult transferToMerchant(Long paymentId)
    {
        try {
            OfflinePayment payment = selectOfflinePaymentByPaymentId(paymentId);
            if (payment == null) {
                return AjaxResult.error("支付记录不存在");
            }
            if (!"1".equals(payment.getPayStatus())) {
                return AjaxResult.error("订单未支付成功");
            }
            if (!"0".equals(payment.getTransferStatus())) {
                return AjaxResult.error("已处理转账");
            }
            // 查询商家信息
            MerchantApplication merchant = merchantApplicationService.selectMerchantApplicationByApplicationId(payment.getMerchantId());
            AppUser user = appUserServiceImpl.selectAppUserByUserId(merchant.getUserId());
            if (StringUtils.isNull(user) || StringUtils.isEmpty(user.getAlipayAccount())) {
                return AjaxResult.error("支付宝账号信息不完整");
            }

            // 更新转账状态为转账中
            payment.setTransferStatus("1");
            payment.setUpdateTime(DateUtils.getNowDate());
            updateOfflinePayment(payment);

            // 生成转账单号
            String transferNo = "TF" + System.currentTimeMillis();

            // 调用支付宝转账接口
            String remark = "线下支付分账-订单号:" + payment.getOrderNo();
            Map<String, Object> transferResult = alipayService.transfer(
                transferNo, user.getAlipayAccount(),
                payment.getTransferAmount(), user.getAlipayName(),
                remark
            );

            if ((Boolean) transferResult.get("success")) {
                // 转账成功
                payment.setTransferStatus("2");
                payment.setTransferNo(transferNo);
                payment.setTransferTime(DateUtils.getNowDate());
                payment.setUpdateTime(DateUtils.getNowDate());
                updateOfflinePayment(payment);
                log.info("线下支付转账成功，支付记录ID：{}，转账单号：{}", paymentId, transferNo);
                return AjaxResult.success("转账成功");
            } else {
                // 转账失败
                payment.setTransferStatus("3");
                payment.setUpdateTime(DateUtils.getNowDate());
                updateOfflinePayment(payment);
                log.error("线下支付转账失败，支付记录ID：{}，错误：{}", paymentId, transferResult.get("errorMsg"));
                return AjaxResult.success("转账失败：" + transferResult.get("errorMsg"));
            }
        } catch (Exception e) {
            log.error("线下支付转账异常，支付记录ID：{}", paymentId, e);
            return AjaxResult.success("转账异常：请联系管理员");
        }
    }

    /**
     * 查询支付状态
     *
     * @param orderNo 订单号
     * @return 支付状态
     */
    @Override
    public String getPaymentStatus(String orderNo)
    {
        try {
            OfflinePayment payment = selectOfflinePaymentByOrderNo(orderNo);
            if (payment == null) {
                return "0"; // 订单不存在，返回待支付
            }
            return payment.getPayStatus();
        } catch (Exception e) {
            log.error("查询线下支付状态异常，订单号：{}", orderNo, e);
            return "0";
        }
    }
}
