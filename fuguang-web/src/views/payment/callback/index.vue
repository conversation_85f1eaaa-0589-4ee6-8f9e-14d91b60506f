<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNo">
        <el-input v-model="queryParams.orderNo" placeholder="请输入订单号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="支付方式" prop="payType">
        <el-select v-model="queryParams.payType" placeholder="请选择支付方式" clearable>
          <el-option label="支付宝" value="1" />
          <el-option label="微信支付" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="回调状态" prop="callbackStatus">
        <el-select v-model="queryParams.callbackStatus" placeholder="请选择回调状态" clearable>
          <el-option label="成功" value="1" />
          <el-option label="失败" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型" prop="businessType">
        <el-select v-model="queryParams.businessType" placeholder="请选择业务类型" clearable>
          <el-option label="任务支付" value="task" />
          <el-option label="商城支付" value="mall" />
          <el-option label="线下支付" value="offline" />
        </el-select>
      </el-form-item>
      <el-form-item label="回调时间">
        <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['payment:callback:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['payment:callback:export']">导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-refresh" size="mini" :disabled="multiple" @click="handleRetry"
          v-hasPermi="['payment:callback:retry']">重试回调</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb8">
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>今日回调总数</span>
          </div>
          <div class="text item">
            <span class="count">{{ statistics.todayTotal }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>成功回调</span>
          </div>
          <div class="text item">
            <span class="count success">{{ statistics.successCount }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>失败回调</span>
          </div>
          <div class="text item">
            <span class="count failed">{{ statistics.failedCount }}</span>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>成功率</span>
          </div>
          <div class="text item">
            <span class="count">{{ statistics.successRate }}%</span>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="callbackList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="回调ID" align="center" prop="callbackId" width="80" />
      <el-table-column label="订单号" align="center" prop="orderNo" width="180" />
      <el-table-column label="业务类型" align="center" prop="businessType" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.businessType === 'task'" type="primary">任务支付</el-tag>
          <el-tag v-else-if="scope.row.businessType === 'mall'" type="success">商城支付</el-tag>
          <el-tag v-else-if="scope.row.businessType === 'offline'" type="warning">线下支付</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="payType" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.pay_type" :value="scope.row.payType" />
        </template>
      </el-table-column>
      <el-table-column label="支付金额" align="center" prop="payAmount" width="120">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold;">¥{{ scope.row.payAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="回调状态" align="center" prop="callbackStatus" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.callbackStatus === '1'" type="success">成功</el-tag>
          <el-tag v-else type="danger">失败</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="重试次数" align="center" prop="retryCount" width="100" />
      <el-table-column label="错误信息" align="center" prop="errorMsg" show-overflow-tooltip />
      <el-table-column label="回调时间" align="center" prop="callbackTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.callbackTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)"
            v-hasPermi="['payment:callback:query']">详情</el-button>
          <el-button size="mini" type="text" icon="el-icon-refresh" @click="handleRetryOne(scope.row)"
            v-hasPermi="['payment:callback:retry']" v-if="scope.row.status === '0'">重试</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDeleteOne(scope.row)"
            v-hasPermi="['payment:callback:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 回调详情对话框 -->
    <el-dialog title="回调详情" :visible.sync="open" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="回调ID">{{ form.callbackId }}</el-descriptions-item>
        <el-descriptions-item label="订单号">{{ form.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="业务类型">{{ form.businessType }}</el-descriptions-item>
        <el-descriptions-item label="支付方式">
          <dict-tag :options="dict.type.pay_type" :value="form.payType" />
        </el-descriptions-item>
        <el-descriptions-item label="支付金额">¥{{ form.payAmount }}</el-descriptions-item>
        <el-descriptions-item label="回调状态">
          <el-tag v-if="form.callbackStatus === '1'" type="success">成功</el-tag>
          <el-tag v-else type="danger">失败</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="重试次数">{{ form.retryCount }}</el-descriptions-item>
        <el-descriptions-item label="回调时间">{{ parseTime(form.callbackTime, '{y}-{m}-{d} {h}:{i}:{s}')
        }}</el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="left">回调参数</el-divider>
      <el-input v-model="form.callbackParams" type="textarea" :rows="8" readonly placeholder="回调参数" />

      <el-divider content-position="left" v-if="form.errorMsg">错误信息</el-divider>
      <el-alert v-if="form.errorMsg" :title="form.errorMsg" type="error" :closable="false" show-icon />

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
        <el-button type="primary" @click="handleRetryOne(form)" v-if="form.status === '0'">重试回调</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPaymentCallback, getPaymentCallback, delPaymentCallback, delPaymentCallbacks, retryPaymentCallback } from '@/api/payment/callback'

export default {
  name: "PaymentCallback",
  dicts: ['pay_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 回调日志表格数据
      callbackList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        orderNo: null,
        payType: null,
        callbackStatus: null,
        businessType: null,
      },
      // 表单参数
      form: {},
      // 统计数据
      statistics: {
        todayTotal: 0,
        successCount: 0,
        failedCount: 0,
        successRate: 0
      }
    };
  },
  created() {
    this.getList();
    this.getStatistics();
  },
  methods: {
    /** 查询回调日志列表 */
    getList() {
      this.loading = true;
      listPaymentCallback(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.callbackList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(error => {
        console.error('获取回调日志列表失败:', error);
        this.$modal.msgError("获取回调日志列表失败");
        this.loading = false;
      });
    },

    /** 获取统计数据 */
    getStatistics() {
      // 获取今日回调统计
      this.$http.get('/payment/callback/todayStatistics').then(response => {
        if (response.data && response.data.code === 200) {
          const data = response.data.data;
          this.statistics = {
            todayTotal: data.todayTotal || 0,
            successCount: data.todaySuccess || 0,
            failedCount: data.todayFailed || 0,
            successRate: data.todaySuccessRate || 0
          };
        }
      }).catch(error => {
        console.error('获取回调统计失败:', error);
        // 使用默认值
        this.statistics = {
          todayTotal: 0,
          successCount: 0,
          failedCount: 0,
          successRate: 0
        };
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    // 表单重置
    reset() {
      this.form = {};
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.callbackId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 查看详情按钮操作 */
    handleView(row) {
      this.reset();
      const callbackId = row.callbackId || this.ids[0];
      getPaymentCallback(callbackId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "回调详情";
      }).catch(error => {
        console.error('获取回调详情失败:', error);
        this.$modal.msgError("获取回调详情失败");
      });
    },

    /** 删除按钮操作 */
    handleDelete() {
      const callbackIds = this.ids;
      this.$modal.confirm('是否确认删除回调日志编号为"' + callbackIds + '"的数据项？').then(function () {
        return delPaymentCallbacks(callbackIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },

    /** 单个删除 */
    handleDeleteOne(row) {
      this.$modal.confirm('是否确认删除该回调日志？').then(function () {
        return delPaymentCallback(row.callbackId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },

    /** 重试回调 */
    handleRetry() {
      const callbackIds = this.ids;
      this.$modal.confirm('是否确认重试选中的回调？').then(() => {
        return this.$http.post('/payment/callback/batchRetry', callbackIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("重试成功");
      }).catch(() => { });
    },

    /** 单个重试 */
    handleRetryOne(row) {
      this.$modal.confirm('是否确认重试该回调？').then(() => {
        return retryPaymentCallback(row.callbackId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("重试成功");
        this.open = false;
      }).catch(() => { });
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('payment/callback/export', {
        ...this.queryParams
      }, `callback_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.text {
  font-size: 14px;
}

.item {
  margin-bottom: 18px;
}

.count {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  display: block;
}

.count.success {
  color: #67C23A;
}

.count.failed {
  color: #F56C6C;
}
</style>
